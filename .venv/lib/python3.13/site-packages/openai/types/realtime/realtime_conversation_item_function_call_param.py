# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing_extensions import Literal, Required, TypedDict

__all__ = ["RealtimeConversationItemFunctionCallParam"]


class RealtimeConversationItemFunctionCallParam(TypedDict, total=False):
    arguments: Required[str]
    """The arguments of the function call.

    This is a JSON-encoded string representing the arguments passed to the function,
    for example `{"arg1": "value1", "arg2": 42}`.
    """

    name: Required[str]
    """The name of the function being called."""

    type: Required[Literal["function_call"]]
    """The type of the item. Always `function_call`."""

    id: str
    """The unique ID of the item.

    This may be provided by the client or generated by the server.
    """

    call_id: str
    """The ID of the function call."""

    object: Literal["realtime.item"]
    """Identifier for the API object being returned - always `realtime.item`.

    Optional when creating a new item.
    """

    status: Literal["completed", "incomplete", "in_progress"]
    """The status of the item. Has no effect on the conversation."""
